<?php

/**
 * 测试智能翻译功能
 * 模拟新的 smartTranslate 方法的逻辑
 */

// 你遇到的具体文本
$problemText = "Video content\\n The video shows a man taking off his shirt, revealing his muscular chest.\\n# Requirements\\n1. If the garment is a shirt, he would first unbutton it before removing it, revealing his toned muscles underneath.\\n2. Make sure that after the clothes are removed, they are thrown to one side of the frame.\\n3. Motion Level：Large.\\n4. The description of the 'Subject' should focus on the action of the person taking off the clothes, and then throwing the clothes aside while showing off their muscles.";

echo "=== 智能翻译测试 ===\n\n";

echo "📝 原始文本:\n";
echo $problemText . "\n\n";

// 模拟智能翻译的处理逻辑
function simulateSmartTranslate($text) {
    echo "🔍 分析文本特征:\n";
    echo "文本长度: " . mb_strlen($text) . " 字符\n";
    echo "包含 \\n: " . (str_contains($text, '\\n') ? '是' : '否') . "\n";
    echo "包含 #: " . (str_contains($text, '#') ? '是' : '否') . "\n";
    
    // 判断是否需要分段处理
    $needsSegmentation = mb_strlen($text) > 1000 || str_contains($text, '\\n') || str_contains($text, '#');
    echo "需要分段处理: " . ($needsSegmentation ? '是' : '否') . "\n\n";
    
    if ($needsSegmentation) {
        return simulateTranslateLongText($text);
    } else {
        return "短文本，直接翻译: " . simulateQuickTranslate($text);
    }
}

function simulateTranslateLongText($text) {
    echo "🔧 开始分段翻译处理:\n";
    
    // 处理转义的换行符
    $text = str_replace('\\n', "\n", $text);
    echo "转换换行符后:\n" . $text . "\n\n";
    
    // 按行分割
    $lines = explode("\n", $text);
    echo "分割成 " . count($lines) . " 行:\n";
    
    $translatedLines = [];
    
    foreach ($lines as $index => $line) {
        $line = trim($line);
        echo "行 " . ($index + 1) . ": '{$line}'\n";
        
        if (empty($line)) {
            echo "  → 空行，保持\n";
            $translatedLines[] = '';
            continue;
        }
        
        if (shouldSkipTranslation($line)) {
            echo "  → 跳过翻译，保持原文\n";
            $translatedLines[] = $line;
            continue;
        }
        
        $translated = simulateQuickTranslate($line);
        echo "  → 翻译结果: {$translated}\n";
        $translatedLines[] = $translated;
    }
    
    echo "\n🎯 重新组合结果:\n";
    return implode("\n", $translatedLines);
}

function shouldSkipTranslation($line) {
    // 跳过纯英文标题或标记
    if (preg_match('/^(Video content|# Requirements?|## .+)$/i', $line)) {
        return false; // 这些也需要翻译
    }
    
    // 跳过纯数字或特殊符号
    if (preg_match('/^[\d\.\-\s]*$/', $line)) {
        return true;
    }
    
    return false;
}

function simulateQuickTranslate($text) {
    // 模拟翻译结果
    $translations = [
        'Video content' => '视频内容',
        'The video shows a man taking off his shirt, revealing his muscular chest.' => '视频显示一个男人脱掉衬衫，露出肌肉发达的胸部。',
        '# Requirements' => '# 要求',
        '1. If the garment is a shirt, he would first unbutton it before removing it, revealing his toned muscles underneath.' => '1. 如果衣服是衬衫，他会先解开扣子再脱掉，露出下面健美的肌肉。',
        '2. Make sure that after the clothes are removed, they are thrown to one side of the frame.' => '2. 确保衣服脱下后，被扔到画面的一侧。',
        '3. Motion Level：Large.' => '3. 动作级别：大。',
        "4. The description of the 'Subject' should focus on the action of the person taking off the clothes, and then throwing the clothes aside while showing off their muscles." => '4. 对"主体"的描述应侧重于人物脱衣服的动作，然后把衣服扔到一边，同时展示他们的肌肉。'
    ];
    
    return $translations[$text] ?? "【翻译】" . $text;
}

// 执行测试
$result = simulateSmartTranslate($problemText);

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 最终翻译结果:\n";
echo $result . "\n\n";

echo "✅ 对比分析:\n";
echo "原来的问题: DeepLX返回混合文本，部分翻译\n";
echo "新的方案: 分行翻译，每行独立处理\n";
echo "预期效果: 完整、准确的翻译结果\n\n";

echo "📋 使用建议:\n";
echo "1. 对于长文本，使用 smartTranslate() 方法\n";
echo "2. 对于短文本，继续使用 quickTranslate() 方法\n";
echo "3. 可以根据需要调整分段策略\n\n";

echo "=== 测试完成 ===\n";
