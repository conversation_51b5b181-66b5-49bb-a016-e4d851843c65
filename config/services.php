<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain'   => env('MAILGUN_DOMAIN'),
        'secret'   => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme'   => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses'    => [
        'key'    => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    'deeplx' => [
        'url'           => env('DEEPLX_API_URL', 'https://api.deeplx.org/mO2Q7QqujsC3AvQjBZohiWG_k/translate'),
        'timeout'       => env('DEEPLX_TIMEOUT', 30),
        'cache_enabled' => env('DEEPLX_CACHE_ENABLED', true),
        'cache_time'    => env('DEEPLX_CACHE_TIME', 3600), // 1小时
    ]
];
