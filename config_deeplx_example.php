<?php

/**
 * DeepLX 配置示例
 * 
 * 将以下配置添加到 config/services.php 文件中
 */

return [
    // 其他服务配置...
    
    'deeplx' => [
        // DeepLX API 地址
        'url' => env('DEEPLX_URL', 'http://localhost:1188/translate'),
        
        // 请求超时时间（秒）
        'timeout' => env('DEEPLX_TIMEOUT', 30),
        
        // 是否启用缓存
        'cache_enabled' => env('DEEPLX_CACHE_ENABLED', true),
        
        // 缓存时间（秒）
        'cache_time' => env('DEEPLX_CACHE_TIME', 3600), // 1小时
    ],
];

/**
 * 环境变量配置示例
 * 
 * 将以下内容添加到 .env 文件中：
 * 
 * # DeepLX 翻译服务配置
 * DEEPLX_URL=http://localhost:1188/translate
 * DEEPLX_TIMEOUT=30
 * DEEPLX_CACHE_ENABLED=true
 * DEEPLX_CACHE_TIME=3600
 */

/**
 * 使用示例：
 * 
 * // 基本使用
 * $service = new \App\Services\DeepLXTranslationService();
 * $result = $service->translateText('Hello, world!', 'ZH');
 * echo $result; // 输出：你好，世界！
 * 
 * // 快速翻译（推荐）
 * $result = $service->quickTranslate('Hello, world!', 'ZH');
 * echo $result; // 输出：你好，世界！
 * 
 * // 批量翻译
 * $texts = ['Hello', 'World', 'Good morning'];
 * $results = $service->translateTexts($texts, 'ZH');
 * print_r($results);
 * 
 * // 调试翻译过程
 * $debug = $service->debugTranslation('Hello, world!', 'ZH');
 * print_r($debug);
 */
