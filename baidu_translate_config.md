# 百度翻译配置指南

## 🔧 配置步骤

### 1. 在 config/services.php 中添加配置

```php
'baidu_translate' => [
    'app_id' => env('BAIDU_TRANSLATE_APP_ID'),
    'app_key' => env('BAIDU_TRANSLATE_APP_KEY'),
    'url' => env('BAIDU_TRANSLATE_URL', 'https://fanyi-api.baidu.com/api/trans/vip/translate'),
    'timeout' => env('BAIDU_TRANSLATE_TIMEOUT', 30),
],
```

### 2. 在 .env 文件中添加你的API密钥

```env
# 百度翻译配置
BAIDU_TRANSLATE_APP_ID=你的APP_ID
BAIDU_TRANSLATE_APP_KEY=你的APP_KEY
BAIDU_TRANSLATE_TIMEOUT=30
```

### 3. 获取百度翻译API密钥

1. 访问：https://fanyi-api.baidu.com/
2. 登录百度账号
3. 进入管理控制台
4. 创建应用获取 APP ID 和 APP KEY

## 🚀 使用方法

### 在SkyxuController中使用

```php
use App\Services\BaiduTranslationService;

// 替换DeepLX翻译
$baiduService = new BaiduTranslationService();

// 翻译Vidu prompt
$result = $baiduService->translateViduPrompt($testText);
echo "百度翻译结果: {$result}";

// 测试连接
$test = $baiduService->testConnection();
if ($test['success']) {
    echo "✅ 百度翻译连接正常";
} else {
    echo "❌ 连接失败: " . $test['error'];
}
```

### 通用翻译方法

```php
// 英文翻译成中文
$result = $baiduService->translate('Hello, world!', 'zh', 'en');

// 自动检测语言翻译成中文
$result = $baiduService->translate('Hello, world!', 'zh', 'auto');

// 中文翻译成英文
$result = $baiduService->translate('你好，世界！', 'en', 'zh');
```

## 📋 支持的语言代码

- `auto` - 自动检测
- `zh` - 中文
- `en` - 英文
- `jp` - 日语
- `kor` - 韩语
- `spa` - 西班牙语
- `fra` - 法语
- `th` - 泰语
- `ara` - 阿拉伯语
- `ru` - 俄语
- `pt` - 葡萄牙语
- `de` - 德语
- `it` - 意大利语
- `el` - 希腊语
- `nl` - 荷兰语
- `pl` - 波兰语
- `bul` - 保加利亚语
- `est` - 爱沙尼亚语
- `dan` - 丹麦语
- `fin` - 芬兰语
- `cs` - 捷克语
- `rom` - 罗马尼亚语
- `slo` - 斯洛文尼亚语
- `swe` - 瑞典语
- `hu` - 匈牙利语
- `vie` - 越南语

## 🎯 优势

1. **稳定可靠** - 百度翻译是成熟的商业服务
2. **免费额度充足** - 每月100万字符（个人认证）
3. **翻译质量高** - 特别是中英文翻译
4. **格式保持好** - 不会出现DeepLX的混合问题
5. **API简单** - 集成容易，调试方便

## 🔍 错误代码说明

- `52001` - 请求超时
- `52002` - 系统错误
- `52003` - 未授权用户
- `54000` - 必填参数为空
- `54001` - 签名错误
- `54003` - 访问频率受限
- `54004` - 账户余额不足
- `54005` - 长query请求频繁
- `58000` - 客户端IP非法
- `58001` - 译文语言方向不支持
- `58002` - 服务当前已关闭
- `90107` - 认证未通过或未生效

## 💡 使用建议

1. **先测试连接** - 确保API密钥正确
2. **查看日志** - 出问题时检查Laravel日志
3. **控制频率** - 注意API调用频率限制
4. **监控用量** - 定期检查字符使用量

现在你可以用百度翻译替代有问题的DeepLX了！
