<?php

/**
 * 测试特定的翻译问题
 * 分析你遇到的具体翻译结果
 */

// 原文
$originalText = "Video content\\n The video shows a man taking off his shirt, revealing his muscular chest.\\n# Requirements\\n1. If the garment is a shirt, he would first unbutton it before removing it, revealing his toned muscles underneath.\\n2. Make sure that after the clothes are removed, they are thrown to one side of the frame.\\n3. Motion Level：Large.\\n4. The description of the 'Subject' should focus on the action of the person taking off the clothes, and then throwing the clothes aside while showing off their muscles.";

// DeepLX返回的混合结果
$mixedResult = "Video content\n The video shows a man taking off his shirt, revealing his muscular chest.\n# Requirements\n1.如果衣服是衬衫，他会先解开扣子再脱掉，露出下面健美的肌肉。确保衣服脱下后，被扔到画面的一侧。动作级别：大。对 \"主体 \"的描述应侧重于人物脱衣服的动作，然后把衣服扔到一边，同时展示他们的肌肉。";

echo "=== 特定翻译问题分析 ===\n\n";

echo "📝 原文:\n";
echo $originalText . "\n\n";

echo "🔄 DeepLX返回的混合结果:\n";
echo $mixedResult . "\n\n";

echo "🔍 问题分析:\n";
echo "1. 这不是标准的 '原文 -> 译文' 格式\n";
echo "2. 前半部分保持英文，后半部分被翻译\n";
echo "3. 这是部分翻译的结果\n\n";

// 分析文本结构
echo "📊 文本结构分析:\n";
$originalLines = explode("\\n", $originalText);
$resultLines = explode("\n", $mixedResult);

echo "原文行数: " . count($originalLines) . "\n";
echo "结果行数: " . count($resultLines) . "\n\n";

echo "逐行对比:\n";
for ($i = 0; $i < max(count($originalLines), count($resultLines)); $i++) {
    $origLine = $originalLines[$i] ?? '';
    $resultLine = $resultLines[$i] ?? '';
    
    echo "行 " . ($i + 1) . ":\n";
    echo "  原文: " . $origLine . "\n";
    echo "  结果: " . $resultLine . "\n";
    
    if ($origLine === $resultLine) {
        echo "  状态: ✅ 相同\n";
    } elseif (empty($resultLine)) {
        echo "  状态: ❌ 结果为空\n";
    } else {
        echo "  状态: 🔄 已翻译\n";
    }
    echo "\n";
}

// 尝试提取纯中文部分
echo "🎯 提取纯中文翻译:\n";

function extractChineseTranslation($text) {
    // 使用正则表达式提取中文内容
    $pattern = '/[\x{4e00}-\x{9fff}\x{ff0c}\x{3002}\x{ff1f}\x{ff01}\x{ff1a}\x{ff1b}\x{201c}\x{201d}\x{2018}\x{2019}\x{ff08}\x{ff09}\x{3001}\s\d\.]+/u';
    preg_match_all($pattern, $text, $matches);
    
    if (!empty($matches[0])) {
        $chineseTexts = array_filter($matches[0], function($item) {
            return mb_strlen(trim($item), 'UTF-8') > 3;
        });
        
        return implode(' ', $chineseTexts);
    }
    
    return '';
}

$chineseOnly = extractChineseTranslation($mixedResult);
echo "提取的中文内容:\n";
echo $chineseOnly . "\n\n";

// 理想的完整翻译应该是什么
echo "💡 理想的完整翻译应该是:\n";
$idealTranslation = "视频内容：视频显示一个男人脱掉衬衫，露出肌肉发达的胸部。要求：1. 如果衣服是衬衫，他会先解开扣子再脱掉，露出下面健美的肌肉。2. 确保衣服脱下后，被扔到画面的一侧。3. 动作级别：大。4. 对主体的描述应侧重于人物脱衣服的动作，然后把衣服扔到一边，同时展示他们的肌肉。";
echo $idealTranslation . "\n\n";

echo "🔧 可能的解决方案:\n";
echo "1. 检查DeepLX服务器配置，确保完整翻译\n";
echo "2. 尝试分段翻译长文本\n";
echo "3. 检查文本编码问题（\\n vs \n）\n";
echo "4. 使用不同的DeepLX参数\n\n";

echo "📋 建议的测试步骤:\n";
echo "1. 测试短文本翻译是否正常\n";
echo "2. 测试不包含特殊字符的文本\n";
echo "3. 检查DeepLX服务器日志\n";
echo "4. 尝试其他翻译服务对比\n\n";

echo "=== 分析完成 ===\n";
