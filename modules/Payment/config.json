[{"name": "基础配置", "type": "tabs", "key": "payment", "list": [{"name": "测试支付", "type": "radio", "key": "PAYMENT_TEST", "value": "1", "source": {"0": "否", "1": "是"}}, {"name": "支付日志", "type": "radio", "key": "LOGGER_ENABLE", "value": "1", "source": {"0": "关闭", "1": "开启"}}, {"name": "开启余额储值", "type": "radio", "key": "CAN_RECHARGE", "value": "0", "source": {"0": "关闭", "1": "开启"}}, {"name": "可充值类型", "type": "checkbox", "key": "CAN_RECHARGE_TYPES", "value": "", "source": {"balance": "余额", "score": "积分", "draw": "绘画", "video": "视频", "audio": "音频"}}]}, {"name": "微信支付", "type": "tabs", "key": "payment_wechat", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_WECHAT", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_WECHAT", "value": "", "source": {"mp": "公众号支付", "wap": "手机网站支付", "app": "APP支付", "scan": "扫码支付", "mini": "小程序支付"}}, {"name": "商户号", "type": "string", "key": "WECHAT_MCH_ID", "value": ""}, {"name": "商户密钥V3", "type": "string", "key": "WECHAT_MCH_SECRET_KEY", "value": ""}, {"name": "商户私钥", "type": "textarea", "key": "WECHAT_MCH_SECRET_CERT", "help": "商户私钥不能带有-----BEGIN RSA PRIVATE KEY-----头尾信息，不要换行", "value": ""}, {"name": "商户公钥证书", "type": "textarea", "key": "WECHAT_MCH_PUBLIC_CERT_PATH", "value": ""}, {"name": "公众号APP_ID", "type": "string", "key": "WECHAT_MP_APP_ID", "value": ""}, {"name": "小程序APP_ID", "type": "string", "key": "WECHAT_MINI_APP_ID", "value": ""}, {"name": "APP-APP_ID", "type": "string", "key": "WECHAT_APP_ID", "value": ""}]}, {"name": "支付宝", "type": "tabs", "key": "payment_alipay", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_ALIPAY", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_ALIPAY", "value": "", "source": {"web": "电脑支付", "wap": "手机网站支付", "app": "APP支付", "pos": "刷卡支付", "scan": "扫码支付", "mini": "小程序支付"}}, {"name": "APP_ID", "type": "string", "key": "ALIPAY_APP_ID", "value": ""}, {"name": "应用私钥", "type": "string", "key": "ALIPAY_APP_SECRET_CERT", "value": ""}, {"name": "应用公钥证书", "type": "textarea", "key": "ALIPAY_APP_PUBLIC_CERT_PATH", "value": ""}, {"name": "支付宝公钥证书", "type": "textarea", "key": "ALIPAY_PUBLIC_CERT_PATH", "value": ""}, {"name": "支付宝根证书", "type": "textarea", "key": "ALIPAY_ROOT_CERT_PATH", "value": ""}, {"name": "RETURN_URL", "type": "string", "key": "ALIPAY_RETURN_URL", "value": ""}]}, {"name": "<PERSON><PERSON>", "type": "tabs", "key": "payment_paypal", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_PAYPAL", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付模式", "type": "radio", "key": "GATEWAY_CHANNEL_PAYPAL", "value": "", "source": {"normal": "正常", "sandbox": "沙箱"}}, {"name": "收款账号", "type": "string", "key": "PAYPAL_ACCOUNT_EMAIL", "value": ""}, {"name": "沙箱账号", "type": "string", "key": "PAYPAL_SAND_EMAIL", "value": ""}]}, {"name": "银联支付", "type": "tabs", "key": "payment_unipay", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_UNIPAY", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_UNIPAY", "value": "", "source": {"web": "电脑支付", "wap": "手机网站支付", "scan": "扫码支付", "pos": "刷卡支付"}}, {"name": "商户号", "type": "string", "key": "UNIPAY_MCH_ID", "value": ""}, {"name": "商户公私钥", "type": "string", "key": "UNIPAY_MCH_CERT_PATH", "value": ""}, {"name": "商户公私钥密码", "type": "string", "key": "UNIPAY_MCH_CERT_PASSWORD", "value": ""}, {"name": "银联公钥证书", "type": "string", "key": "UNIPAY_PUBLIC_CERT_PATH", "value": ""}, {"name": "return_url", "type": "string", "key": "UNIPAY_RETURN_URL", "value": ""}]}, {"name": "京东快捷", "type": "tabs", "key": "payment_jd_quick", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_JD_QUICK", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_JD_QUICK", "value": "", "source": {"web": "电脑支付", "wap": "手机网站支付"}}]}, {"name": "银联商务", "type": "tabs", "key": "payment_china_ums", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_CHINA_UMS", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_CHINA_UMS", "value": "", "source": {"web": "电脑支付", "wap": "手机网站支付"}}]}, {"name": "余额支付", "type": "tabs", "key": "payment_balance", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_BALANCE", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_BALANCE", "value": "", "source": {"balance": "余额", "score": "积分", "coins": "代币", "cash": "现金", "other": "其他"}}]}, {"name": "汇付斗拱", "type": "tabs", "key": "payment_dou_gong", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_DOU_GONG", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_DOU_GONG", "value": "", "source": {"wechat": "微信", "alipay": "支付宝"}}, {"name": "PRODUCT_ID", "type": "string", "key": "DOU_GONG_PRODUCT_ID", "value": ""}, {"name": "DOU_GONG_HUIFU_ID", "type": "string", "key": "DOU_GONG_HUIFU_ID", "value": ""}, {"name": "SEQ_ID", "type": "string", "key": "DOU_GONG_SEQ_ID", "value": ""}, {"name": "public_key", "type": "string", "key": "DOU_GONG_PUBLIC_KEY", "value": ""}, {"name": "private_key", "type": "string", "key": "DOU_GONG_PRIVATE_KEY", "value": ""}, {"name": "huifu_public_key", "type": "string", "key": "DOU_GONG_HUIFU_PUBLIC_KEY", "value": ""}]}, {"name": "拉卡拉", "type": "tabs", "key": "payment_lakala", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_LAKALA", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付环境", "type": "radio", "key": "LAKALA_ENV", "value": "sandbox", "source": {"normal": "正式环境", "sandbox": "沙盒模式"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_LAKALA", "value": "", "source": {"ccss": "收银台", "scan_wechat": "聚合主扫(微信)", "scan_alipay": "聚合主扫(支付宝)"}}, {"name": "APP_ID", "type": "string", "key": "LAKALA_APP_ID", "value": ""}, {"name": "商户号", "type": "string", "key": "LAKALA_MERCHANT_NO", "value": ""}, {"name": "终端号", "type": "string", "key": "LAKALA_TERM_NO", "value": ""}, {"name": "证书序列号", "type": "string", "key": "LAKALA_MCH_SERIAL_NO", "value": ""}, {"name": "实体终端号", "type": "string", "key": "LAKALA_VPOS_ID", "value": ""}, {"name": "拉卡拉验签公钥", "type": "textarea", "key": "LAKALA_PUBLIC_KEY", "value": ""}, {"name": "商户自建私钥", "type": "textarea", "key": "LAKALA_PRIVATE_KEY", "value": ""}]}, {"name": "台州银行", "type": "tabs", "key": "payment_tz_bank", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_TZ_BANK", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_TZ_BANK", "value": "", "source": {"wechat": "微信", "alipay": "支付宝"}}, {"name": "APP_KEY", "type": "string", "key": "TZ_BANK_APP_KEY", "value": ""}, {"name": "APP_SECRET", "type": "string", "key": "TZ_BANK_APP_SECRET", "value": ""}, {"name": "账户私钥", "type": "string", "key": "TZ_BANK_PRIVATE_KEY", "value": ""}, {"name": "账户公钥", "type": "string", "key": "TZ_BANK_PUBLIC_KEY", "value": ""}, {"name": "银行公钥", "type": "string", "key": "TZ_BANK_BANK_PUBLIC_KEY", "value": ""}]}, {"name": "对公账户", "type": "tabs", "key": "payment_corporate", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_CORPORATE", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_CORPORATE", "value": "", "source": {"bank": "银行卡"}}, {"name": "公司名称", "type": "string", "key": "CORPORATE_COMPANY_NAME", "value": ""}, {"name": "开户行", "type": "string", "key": "CORPORATE_BANK_NAME", "value": ""}, {"name": "卡号", "type": "string", "key": "CORPORATE_BANK_CARD_NO", "value": ""}]}, {"name": "苹果内购", "type": "tabs", "key": "payment_apple", "list": [{"name": "是否启用", "type": "radio", "key": "GATEWAY_ENABLE_APPLE", "value": "0", "source": {"0": "关闭", "1": "启用"}}, {"name": "支付渠道", "type": "checkbox", "key": "GATEWAY_CHANNEL_APPLE", "value": "", "source": {"apple_iap": "苹果内购", "apple_pay": "苹果支付"}}, {"name": "支付模式", "type": "radio", "key": "APPLE_CHANNEL_PAY", "value": "", "source": {"normal": "正常", "sandbox": "沙箱"}}, {"name": "商户私钥", "type": "textarea", "key": "APPLE_SECRET_CERT", "help": "商户私钥不能带有-----BEGIN RSA PRIVATE KEY-----头尾信息，不要换行", "value": ""}, {"name": "共享密钥", "type": "string", "key": "APPLE_IAP_SHARED_SECRET", "value": ""}]}]