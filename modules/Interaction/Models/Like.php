<?php

namespace Modules\Interaction\Models;

use App\Events\LikeCreatedEvent;
use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Like extends Model
{
    use BelongsToUser, SoftDeletes;

    protected $table = 'interaction_likes';

    protected static function boot()
    {
        parent::boot();
        self::created(function (Like $like) {
            event(new LikeCreatedEvent($like));
        });
    }

    public function likeable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeWithType(Builder $query, string $type): Builder
    {
        return $query->where('likeable_type', app($type)->getMorphClass());
    }

    public function scopeWithTarget(Builder $query, array $target): Builder
    {
        return $query->where('likeable_type', $target['target_type'])
            ->where('likeable_id', $target['target_id']);
    }

    public function scopeOfItem(Builder $query, Model $item): Builder
    {
        return $query->where('likeable_type', $item->getMorphClass())
            ->where('likeable_id', $item->getKey());
    }
}
