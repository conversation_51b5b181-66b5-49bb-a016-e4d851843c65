<?php

/**
 * 测试App友好的翻译功能
 * 解决换行符显示和多行格式问题
 */

// 你遇到的具体文本
$problemText = "Video content\\n The video shows a man taking off his shirt, revealing his muscular chest.\\n# Requirements\\n1. If the garment is a shirt, he would first unbutton it before removing it, revealing his toned muscles underneath.\\n2. Make sure that after the clothes are removed, they are thrown to one side of the frame.\\n3. Motion Level：Large.\\n4. The description of the 'Subject' should focus on the action of the person taking off the clothes, and then throwing the clothes aside while showing off their muscles.";

echo "=== App友好翻译测试 ===\n\n";

echo "📝 原始文本:\n";
echo $problemText . "\n\n";

// 模拟不同的翻译方法
function simulateSmartTranslate($text, $keepLineBreaks = true) {
    // 处理转义的换行符
    $text = str_replace('\\n', "\n", $text);
    
    // 按行分割
    $lines = explode("\n", $text);
    $translatedLines = [];
    
    // 模拟翻译结果
    $translations = [
        'Video content' => '视频内容',
        'The video shows a man taking off his shirt, revealing his muscular chest.' => '视频显示一个男人脱掉衬衫，露出肌肉发达的胸部。',
        '# Requirements' => '要求',
        '1. If the garment is a shirt, he would first unbutton it before removing it, revealing his toned muscles underneath.' => '1. 如果衣服是衬衫，他会先解开扣子再脱掉，露出下面健美的肌肉。',
        '2. Make sure that after the clothes are removed, they are thrown to one side of the frame.' => '2. 确保衣服脱下后，被扔到画面的一侧。',
        '3. Motion Level：Large.' => '3. 动作级别：大。',
        "4. The description of the 'Subject' should focus on the action of the person taking off the clothes, and then throwing the clothes aside while showing off their muscles." => '4. 对"主体"的描述应侧重于人物脱衣服的动作，然后把衣服扔到一边，同时展示他们的肌肉。'
    ];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) {
            if ($keepLineBreaks) {
                $translatedLines[] = '';
            }
            continue;
        }
        
        $translated = $translations[$line] ?? "【翻译】" . $line;
        $translatedLines[] = $translated;
    }
    
    // 根据参数决定如何连接结果
    if ($keepLineBreaks) {
        return implode("\n", $translatedLines);
    } else {
        // 合并为单行，用空格分隔
        return implode(' ', array_filter($translatedLines, function($line) {
            return !empty(trim($line));
        }));
    }
}

function cleanForAppDisplay($text) {
    // 移除所有换行符和多余空格
    $text = preg_replace('/\s+/', ' ', $text);
    
    // 移除可能残留的换行符标记
    $text = str_replace(['\n', '\\n', "\n", "\r"], ' ', $text);
    
    // 清理标题标记，使其更适合app显示
    $text = preg_replace('/^#\s*/', '', $text);
    $text = preg_replace('/\s*#\s*/', ' ', $text);
    
    // 清理多余的空格
    $text = preg_replace('/\s+/', ' ', $text);
    
    return trim($text);
}

echo "🔄 方法1: 保持换行格式 (smartTranslate)\n";
$result1 = simulateSmartTranslate($problemText, true);
echo "结果:\n" . $result1 . "\n\n";
echo "问题: 多行显示，包含换行符，不适合app\n\n";

echo str_repeat("-", 60) . "\n\n";

echo "🔄 方法2: 合并为单行 (smartTranslate with keepLineBreaks=false)\n";
$result2 = simulateSmartTranslate($problemText, false);
echo "结果:\n" . $result2 . "\n\n";
echo "改进: 单行显示，但可能还有格式问题\n\n";

echo str_repeat("-", 60) . "\n\n";

echo "✅ 方法3: App友好翻译 (translateForApp)\n";
$result3 = simulateSmartTranslate($problemText, false);
$result3 = cleanForAppDisplay($result3);
echo "结果:\n" . $result3 . "\n\n";
echo "完美: 单行连续文本，适合app展示\n\n";

echo str_repeat("=", 60) . "\n";
echo "📱 App使用建议:\n\n";

echo "// 推荐用法\n";
echo "\$service = new DeepLXTranslationService();\n";
echo "\$result = \$service->translateForApp(\$text, 'ZH');\n";
echo "// 返回: 单行连续文本，无换行符，适合app显示\n\n";

echo "// 如果需要保持格式\n";
echo "\$result = \$service->smartTranslate(\$text, 'ZH', null, true);\n";
echo "// 返回: 多行格式，适合网页或文档显示\n\n";

echo "// 如果需要合并但保留一些格式\n";
echo "\$result = \$service->smartTranslate(\$text, 'ZH', null, false);\n";
echo "// 返回: 单行但可能包含一些格式标记\n\n";

echo "🎯 解决的问题:\n";
echo "1. ✅ 消除 \\n 字符显示\n";
echo "2. ✅ 合并为单行文本\n";
echo "3. ✅ 适合app界面展示\n";
echo "4. ✅ 保持翻译完整性\n\n";

echo "=== 测试完成 ===\n";
