# DeepLX 翻译服务设置指南

## 问题诊断

如果你的 DeepLX 返回的是原文和译文的混合内容而不是纯译文，可能的原因和解决方案：

### 1. 检查 DeepLX 服务器配置

确保你的 DeepLX 服务器是最新版本：

```bash
# 更新 DeepLX
git clone https://github.com/OwO-Network/DeepLX.git
cd DeepLX
go build -ldflags "-s -w" -o deeplx main.go
./deeplx
```

### 2. 测试 DeepLX 连接

使用我们提供的测试方法：

```php
use App\Services\DeepLXTranslationService;

$service = new DeepLXTranslationService();

// 测试连接和响应格式
$test = $service->testDeepLXConnection();
print_r($test);
```

### 3. 检查 API 响应格式

正确的 DeepLX 响应应该是：

```json
{
    "code": 200,
    "id": "unique-id",
    "data": "你好，世界！",
    "alternatives": ["你好，世界！"]
}
```

如果你看到的是混合格式，比如：

```json
{
    "data": "Hello, world! -> 你好，世界！"
}
```

这说明 DeepLX 服务器配置有问题。

### 4. 使用推荐的方法

```php
// 推荐：使用快速翻译方法
$result = $service->quickTranslate('Hello, world!', 'ZH');
echo $result; // 应该输出：你好，世界！

// 或者使用标准方法
$result = $service->translateText('Hello, world!', 'ZH');
echo $result; // 应该输出：你好，世界！
```

### 5. 调试步骤

如果仍然有问题，按以下步骤调试：

```php
// 1. 测试连接
$connectionTest = $service->testDeepLXConnection();
var_dump($connectionTest);

// 2. 使用调试方法
$debugResult = $service->debugTranslation('Hello, world!', 'ZH');
print_r($debugResult);

// 3. 检查原始响应
echo "Raw API Response: " . $debugResult['raw_api_response'];
echo "Final Result: " . $debugResult['final_result'];
```

## 配置建议

### .env 配置

```env
# DeepLX 服务配置
DEEPLX_URL=http://localhost:1188/translate
DEEPLX_TIMEOUT=30
DEEPLX_CACHE_ENABLED=true
DEEPLX_CACHE_TIME=3600
```

### config/services.php

```php
'deeplx' => [
    'url' => env('DEEPLX_URL', 'http://localhost:1188/translate'),
    'timeout' => env('DEEPLX_TIMEOUT', 30),
    'cache_enabled' => env('DEEPLX_CACHE_ENABLED', true),
    'cache_time' => env('DEEPLX_CACHE_TIME', 3600),
],
```

## 常见问题解决

### 问题1：返回混合文本
**解决方案**：我们的服务已经内置了智能提取功能，会自动处理混合文本。

### 问题2：连接超时
**解决方案**：增加超时时间或检查 DeepLX 服务器状态。

### 问题3：返回空结果
**解决方案**：检查 DeepLX 服务器是否正常运行，以及网络连接。

## 性能优化

1. **启用缓存**：避免重复翻译相同文本
2. **使用 quickTranslate**：针对 DeepLX 优化的快速方法
3. **批量翻译**：使用 `translateTexts` 方法处理多个文本

## 支持的语言代码

- EN: English
- ZH: Chinese
- JA: Japanese
- FR: French
- DE: German
- ES: Spanish
- IT: Italian
- PT: Portuguese
- RU: Russian
- KO: Korean

更多语言代码请参考 DeepL 官方文档。
