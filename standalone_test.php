<?php

/**
 * 独立的 DeepLX 测试脚本
 * 不依赖 Laravel 框架，直接测试 DeepLX API
 */

// 配置
$deeplxUrl = 'http://localhost:1188/translate'; // 修改为你的 DeepLX 地址
$timeout = 30;

function testDeepLX($url, $text, $targetLang, $sourceLang = null) {
    // 准备请求数据
    $data = [
        'text' => $text,
        'target_lang' => strtoupper($targetLang),
    ];
    
    if ($sourceLang) {
        $data['source_lang'] = strtoupper($sourceLang);
    }
    
    // 添加可能的参数
    $data['alternatives'] = 1;
    $data['format'] = 'text';
    
    // 初始化 cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $httpCode === 200 && !$error,
        'http_code' => $httpCode,
        'error' => $error,
        'raw_response' => $response,
        'json_response' => json_decode($response, true),
        'request_data' => $data
    ];
}

function extractTranslation($response, $originalText) {
    if (!is_array($response)) {
        return "响应不是有效的JSON格式";
    }
    
    // 尝试提取 data 字段
    if (isset($response['data']) && is_string($response['data'])) {
        $translated = trim($response['data']);
        echo "  📝 从 data 字段提取: {$translated}\n";
        
        // 检查是否包含原文
        if (strpos($translated, $originalText) !== false) {
            echo "  ⚠️  包含原文，需要进一步处理\n";
            
            // 尝试分割
            $separators = [' -> ', ' → ', ' - ', ' | ', '：', ': '];
            foreach ($separators as $sep) {
                if (strpos($translated, $sep) !== false) {
                    $parts = explode($sep, $translated, 2);
                    if (count($parts) >= 2) {
                        $first = trim($parts[0]);
                        $second = trim($parts[1]);
                        
                        if ($first === $originalText) {
                            echo "  ✅ 提取到纯译文: {$second}\n";
                            return $second;
                        }
                        if ($second === $originalText) {
                            echo "  ✅ 提取到纯译文: {$first}\n";
                            return $first;
                        }
                    }
                }
            }
        }
        
        return $translated;
    }
    
    // 尝试提取 alternatives 字段
    if (isset($response['alternatives']) && is_array($response['alternatives']) && !empty($response['alternatives'])) {
        $translated = trim($response['alternatives'][0]);
        echo "  📝 从 alternatives 字段提取: {$translated}\n";
        return $translated;
    }
    
    // 尝试其他字段
    if (isset($response['text'])) {
        $translated = trim($response['text']);
        echo "  📝 从 text 字段提取: {$translated}\n";
        return $translated;
    }
    
    return "未找到翻译文本";
}

echo "=== DeepLX 独立测试 ===\n\n";
echo "测试 URL: {$deeplxUrl}\n\n";

// 测试文本
$testTexts = [
    'Hello, world!',
    'How are you today?',
    'Good morning!',
    'Thank you very much!'
];

foreach ($testTexts as $index => $text) {
    echo "测试 " . ($index + 1) . ":\n";
    echo "原文: {$text}\n";
    
    $result = testDeepLX($deeplxUrl, $text, 'ZH');
    
    if ($result['success']) {
        echo "✅ 请求成功 (HTTP {$result['http_code']})\n";
        echo "原始响应: {$result['raw_response']}\n";
        
        if ($result['json_response']) {
            echo "响应结构: " . implode(', ', array_keys($result['json_response'])) . "\n";
            $translation = extractTranslation($result['json_response'], $text);
            echo "最终译文: {$translation}\n";
        } else {
            echo "❌ 响应不是有效的JSON格式\n";
        }
    } else {
        echo "❌ 请求失败 (HTTP {$result['http_code']})\n";
        if ($result['error']) {
            echo "错误: {$result['error']}\n";
        }
        echo "响应: {$result['raw_response']}\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "=== 测试完成 ===\n";
echo "\n如果看到错误，请检查:\n";
echo "1. DeepLX 服务是否在 {$deeplxUrl} 运行\n";
echo "2. 防火墙是否阻止了连接\n";
echo "3. URL 是否正确\n";
