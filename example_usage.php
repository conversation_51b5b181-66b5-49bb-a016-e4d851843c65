<?php

/**
 * DeepLX 翻译服务使用示例
 * 
 * 这个文件展示了如何使用优化后的 DeepLXTranslationService
 */

require_once 'vendor/autoload.php';

use App\Services\DeepLXTranslationService;

try {
    // 创建服务实例
    $service = new DeepLXTranslationService();
    
    echo "=== DeepLX 翻译服务使用示例 ===\n\n";
    
    // 示例1: 快速翻译（推荐）
    echo "1. 快速翻译示例:\n";
    $text1 = "Hello, world!";
    $result1 = $service->quickTranslate($text1, 'ZH');
    echo "原文: {$text1}\n";
    echo "译文: {$result1}\n\n";
    
    // 示例2: 标准翻译
    echo "2. 标准翻译示例:\n";
    $text2 = "How are you today?";
    $result2 = $service->translateText($text2, 'ZH');
    echo "原文: {$text2}\n";
    echo "译文: {$result2}\n\n";
    
    // 示例3: 完整翻译信息
    echo "3. 完整翻译信息:\n";
    $text3 = "Good morning!";
    $fullResult = $service->translate($text3, 'ZH');
    echo "原文: {$fullResult['original_text']}\n";
    echo "译文: {$fullResult['translated_text']}\n";
    echo "源语言: {$fullResult['source_language']}\n";
    echo "目标语言: {$fullResult['target_language']}\n";
    echo "服务: {$fullResult['service']}\n\n";
    
    // 示例4: 批量翻译
    echo "4. 批量翻译示例:\n";
    $texts = [
        "Hello",
        "World", 
        "Good morning",
        "Thank you"
    ];
    $batchResults = $service->translateTexts($texts, 'ZH');
    foreach ($texts as $index => $originalText) {
        echo "原文: {$originalText} -> 译文: {$batchResults[$index]}\n";
    }
    echo "\n";
    
    // 示例5: 指定源语言
    echo "5. 指定源语言示例:\n";
    $text5 = "Bonjour le monde";
    $result5 = $service->quickTranslate($text5, 'ZH', 'FR');
    echo "原文 (法语): {$text5}\n";
    echo "译文 (中文): {$result5}\n\n";
    
    // 示例6: 连接测试
    echo "6. 连接测试:\n";
    $connectionTest = $service->testDeepLXConnection();
    if ($connectionTest['success']) {
        echo "✅ DeepLX 连接正常\n";
        echo "响应结构: " . implode(', ', $connectionTest['response_structure']) . "\n";
        if (isset($connectionTest['extracted_data'])) {
            echo "提取的译文: {$connectionTest['extracted_data']}\n";
        }
    } else {
        echo "❌ DeepLX 连接失败: {$connectionTest['error']}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "请检查:\n";
    echo "1. DeepLX 服务是否正在运行\n";
    echo "2. 配置文件中的 URL 是否正确\n";
    echo "3. 网络连接是否正常\n";
}

echo "\n=== 使用完成 ===\n";
