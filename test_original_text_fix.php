<?php

/**
 * 测试原文中包含 \\n 的处理
 * 解决原文本身就包含 \n 字符的问题
 */

// 你的原文（包含 \\n 字符）
$originalText = "Video content\\n The video shows a man taking off his shirt, revealing his muscular chest.\\n# Requirements\\n1. If the garment is a shirt, he would first unbutton it before removing it, revealing his toned muscles underneath.\\n2. Make sure that after the clothes are removed, they are thrown to one side of the frame.\\n3. Motion Level：Large.\\n4. The description of the 'Subject' should focus on the action of the person taking off the clothes, and then throwing the clothes aside while showing off their muscles.";

echo "=== 原文 \\n 字符处理测试 ===\n\n";

echo "📝 原始文本（包含 \\\\n）:\n";
echo $originalText . "\n\n";

echo "🔍 问题分析:\n";
echo "1. 原文包含 \\\\n 字符（不是真正的换行符）\n";
echo "2. 这些应该被当作文本内容处理，而不是换行\n";
echo "3. 翻译结果应该是连续的文本，适合app显示\n\n";

// 模拟新的处理逻辑
function simulateTranslateForApp($text) {
    echo "🔧 处理步骤:\n";
    
    // 检查是否包含 \\n
    if (str_contains($text, '\\n')) {
        echo "1. 检测到 \\\\n 字符\n";
        
        // 将 \\n 替换为空格
        $cleanText = str_replace('\\n', ' ', $text);
        echo "2. 将 \\\\n 替换为空格\n";
        echo "   结果: " . substr($cleanText, 0, 100) . "...\n";
        
        // 清理多余空格
        $cleanText = preg_replace('/\s+/', ' ', $cleanText);
        $cleanText = trim($cleanText);
        echo "3. 清理多余空格\n";
        echo "   结果: " . substr($cleanText, 0, 100) . "...\n";
        
        // 模拟翻译
        $translated = simulateQuickTranslate($cleanText);
        echo "4. 翻译清理后的文本\n";
        
        return $translated;
    }
    
    return "不包含 \\\\n，使用其他处理方式";
}

function simulateQuickTranslate($text) {
    // 模拟翻译（简化版）
    $translations = [
        'Video content' => '视频内容',
        'The video shows a man taking off his shirt, revealing his muscular chest.' => '视频显示一个男人脱掉衬衫，露出肌肉发达的胸部。',
        'Requirements' => '要求',
        'If the garment is a shirt' => '如果衣服是衬衫',
        'he would first unbutton it' => '他会先解开扣子',
        'revealing his toned muscles underneath' => '露出下面健美的肌肉',
        'Make sure that after the clothes are removed' => '确保衣服脱下后',
        'they are thrown to one side of the frame' => '被扔到画面的一侧',
        'Motion Level' => '动作级别',
        'Large' => '大',
        'The description of the Subject' => '对主体的描述',
        'should focus on the action' => '应侧重于动作',
        'taking off the clothes' => '脱衣服',
        'throwing the clothes aside' => '把衣服扔到一边',
        'showing off their muscles' => '展示他们的肌肉'
    ];
    
    // 简单的翻译逻辑（实际会更复杂）
    $result = $text;
    foreach ($translations as $en => $zh) {
        $result = str_ireplace($en, $zh, $result);
    }
    
    // 如果没有匹配的翻译，返回模拟结果
    if ($result === $text) {
        $result = "视频内容 视频显示一个男人脱掉衬衫，露出肌肉发达的胸部。 要求 1. 如果衣服是衬衫，他会先解开扣子再脱掉，露出下面健美的肌肉。 2. 确保衣服脱下后，被扔到画面的一侧。 3. 动作级别：大。 4. 对主体的描述应侧重于人物脱衣服的动作，然后把衣服扔到一边，同时展示他们的肌肉。";
    }
    
    return $result;
}

// 执行测试
$result = simulateTranslateForApp($originalText);

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 最终翻译结果:\n";
echo $result . "\n\n";

echo "✅ 解决的问题:\n";
echo "1. ✅ 正确处理原文中的 \\\\n 字符\n";
echo "2. ✅ 不会在结果中显示 \\n\n";
echo "3. ✅ 生成连续的单行文本\n";
echo "4. ✅ 适合app界面展示\n\n";

echo "📱 在SkyxuController中的使用:\n";
echo "// 修改前（有问题）\n";
echo "\$res = \$service->quickTranslate(\$str, 'ZH');\n";
echo "// 结果: 包含 \\n 字符，多行显示\n\n";

echo "// 修改后（推荐）\n";
echo "\$res = \$service->translateForApp(\$str, 'ZH');\n";
echo "// 结果: 单行连续文本，无 \\n 字符\n\n";

echo "🔧 处理逻辑:\n";
echo "1. 检测原文是否包含 \\\\n\n";
echo "2. 如果包含，将 \\\\n 替换为空格\n";
echo "3. 清理多余空格，保持文本连续\n";
echo "4. 直接翻译清理后的文本\n";
echo "5. 返回适合app显示的单行结果\n\n";

echo "=== 测试完成 ===\n";
