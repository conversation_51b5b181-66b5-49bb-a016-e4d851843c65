<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BaiduTranslationService
{
    private string $appId;
    private string $appKey;
    private string $apiUrl;
    private int $timeout;

    public function __construct()
    {
        $this->appId = config('services.baidu_translate.app_id');
        $this->appKey = config('services.baidu_translate.app_key');
        $this->apiUrl = config('services.baidu_translate.url', 'https://fanyi-api.baidu.com/api/trans/vip/translate');
        $this->timeout = config('services.baidu_translate.timeout', 30);
    }

    /**
     * 翻译文本
     * 
     * @param string $text 要翻译的文本
     * @param string $to 目标语言（zh: 中文, en: 英文）
     * @param string $from 源语言（auto: 自动检测）
     * @return string 翻译结果
     * @throws Exception
     */
    public function translate(string $text, string $to = 'zh', string $from = 'auto'): string
    {
        if (empty($this->appId) || empty($this->appKey)) {
            throw new Exception('百度翻译配置不完整，请检查 APP_ID 和 APP_KEY');
        }

        // 生成随机数
        $salt = rand(10000, 99999);
        
        // 生成签名
        $sign = $this->generateSign($text, $salt);

        // 准备请求参数
        $params = [
            'q' => $text,
            'from' => $from,
            'to' => $to,
            'appid' => $this->appId,
            'salt' => $salt,
            'sign' => $sign
        ];

        Log::info('百度翻译请求', [
            'text' => $text,
            'from' => $from,
            'to' => $to,
            'params' => array_merge($params, ['sign' => '***'])
        ]);

        try {
            // 发送请求
            $response = Http::timeout($this->timeout)
                ->asForm()
                ->post($this->apiUrl, $params);

            if (!$response->successful()) {
                throw new Exception("百度翻译API请求失败: HTTP {$response->status()}");
            }

            $result = $response->json();

            Log::info('百度翻译响应', ['response' => $result]);

            // 检查错误
            if (isset($result['error_code'])) {
                throw new Exception("百度翻译API错误: {$result['error_code']} - {$result['error_msg']}");
            }

            // 提取翻译结果
            if (isset($result['trans_result']) && is_array($result['trans_result']) && !empty($result['trans_result'])) {
                $translatedText = $result['trans_result'][0]['dst'] ?? '';
                
                if (empty($translatedText)) {
                    throw new Exception('百度翻译返回空结果');
                }

                Log::info('百度翻译成功', [
                    'original' => $text,
                    'translated' => $translatedText
                ]);

                return $translatedText;
            }

            throw new Exception('百度翻译响应格式无效');

        } catch (Exception $e) {
            Log::error('百度翻译失败', [
                'error' => $e->getMessage(),
                'text' => $text,
                'from' => $from,
                'to' => $to
            ]);
            throw $e;
        }
    }

    /**
     * 生成签名
     */
    private function generateSign(string $query, int $salt): string
    {
        // 百度翻译签名算法：MD5(appid + query + salt + key)
        $str = $this->appId . $query . $salt . $this->appKey;
        return md5($str);
    }

    /**
     * 测试连接
     */
    public function testConnection(): array
    {
        try {
            $testText = 'Hello, world!';
            $result = $this->translate($testText, 'zh', 'en');
            
            return [
                'success' => true,
                'test_text' => $testText,
                'translated' => $result,
                'message' => '百度翻译连接正常'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => '百度翻译连接失败'
            ];
        }
    }

    /**
     * 翻译Vidu prompt（专门优化）
     */
    public function translateViduPrompt(string $prompt): string
    {
        return $this->translate($prompt, 'zh', 'en');
    }
}
