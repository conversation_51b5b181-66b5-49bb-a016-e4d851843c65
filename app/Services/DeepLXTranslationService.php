<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DeepLXTranslationService
{
    private string $apiUrl;
    private int    $timeout;
    private bool   $cacheEnabled;
    private int    $cacheTime;

    public function __construct()
    {
        $this->apiUrl       = 'https://api.deeplx.org/mO2Q7QqujsC3AvQjBZohbY25Vcr6VXan8sZjD7iWG_k/translate';
        $this->timeout      = config('services.deeplx.timeout', 30);
        $this->cacheEnabled = config('services.deeplx.cache_enabled', true);
        $this->cacheTime    = config('services.deeplx.cache_time', 3600); // 1小时
    }

    /**
     * 翻译文本
     *
     * @param  string  $text  要翻译的文本
     * @param  string  $targetLang  目标语言代码
     * @param  string|null  $sourceLang  源语言代码，null为自动检测
     * @return array
     * @throws Exception
     */
    public function translate(string $text, string $targetLang, ?string $sourceLang = null): array
    {
        // 验证输入
        $this->validateInput($text, $targetLang, $sourceLang);

        // 生成缓存键
        $cacheKey = $this->generateCacheKey($text, $targetLang, $sourceLang);

        // 尝试从缓存获取
        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            Log::info('从缓存获取翻译结果', ['cache_key' => $cacheKey]);
            return Cache::get($cacheKey);
        }

        try {
            // 准备请求数据
            $requestData = [
                'text'        => $text,
                'target_lang' => strtoupper($targetLang),
            ];

            if ($sourceLang) {
                $requestData['source_lang'] = strtoupper($sourceLang);
            }

            Log::info('发送翻译请求', $requestData);

            // 发送请求
            $response = Http::timeout($this->timeout)
                ->post($this->apiUrl, $requestData);

            if (! $response->successful()) {
                throw new Exception(
                    "DeepLX API请求失败: HTTP {$response->status()} - {$response->body()}"
                );
            }

            $result = $response->json();

            // 验证响应格式
            if (! $this->isValidResponse($result)) {
                throw new Exception('DeepLX API返回格式无效');
            }

            // 格式化响应
            $formattedResult = $this->formatResponse($result, $text, $targetLang, $sourceLang);

            // 缓存结果
            if ($this->cacheEnabled) {
                Cache::put($cacheKey, $formattedResult, $this->cacheTime);
                Log::info('翻译结果已缓存', ['cache_key' => $cacheKey]);
            }

            Log::info('翻译完成', ['original_text' => $text, 'translated_text' => $formattedResult['translated_text']]);

            return $formattedResult;
        } catch (Exception $e) {
            Log::error('翻译失败', [
                'error'       => $e->getMessage(),
                'text'        => $text,
                'target_lang' => $targetLang,
                'source_lang' => $sourceLang
            ]);
            throw $e;
        }
    }

    /**
     * 批量翻译
     *
     * @param  array  $texts  文本数组
     * @param  string  $targetLang  目标语言
     * @param  string|null  $sourceLang  源语言
     * @return array
     */
    public function translateBatch(array $texts, string $targetLang, ?string $sourceLang = null): array
    {
        $results = [];
        $errors  = [];

        foreach ($texts as $index => $text) {
            try {
                $results[$index] = $this->translate($text, $targetLang, $sourceLang);
            } catch (Exception $e) {
                $errors[$index] = [
                    'original_text' => $text,
                    'error'         => $e->getMessage(),
                    'index'         => $index
                ];
            }
        }

        return [
            'successful_translations' => $results,
            'failed_translations'     => $errors,
            'total_count'             => count($texts),
            'success_count'           => count($results),
            'error_count'             => count($errors)
        ];
    }

    /**
     * 检测语言
     *
     * @param  string  $text
     * @return array
     */
    public function detectLanguage(string $text): array
    {
        try {
            // 使用翻译接口进行语言检测（通过翻译成英文）
            $result = $this->translate($text, 'EN');
            return [
                'detected_language' => $result['source_language'] ?? 'unknown',
                'confidence'        => $result['confidence'] ?? null
            ];
        } catch (Exception $e) {
            Log::error('语言检测失败', ['error' => $e->getMessage(), 'text' => $text]);
            throw new Exception('语言检测失败: '.$e->getMessage());
        }
    }

    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public function getSupportedLanguages(): array
    {
        return [
            'EN' => 'English',
            'ZH' => 'Chinese',
            'JA' => 'Japanese',
            'FR' => 'French',
            'DE' => 'German',
            'ES' => 'Spanish',
            'IT' => 'Italian',
            'PT' => 'Portuguese',
            'RU' => 'Russian',
            'KO' => 'Korean',
            'AR' => 'Arabic',
            'NL' => 'Dutch',
            'PL' => 'Polish',
            'TR' => 'Turkish'
        ];
    }

    /**
     * 清除翻译缓存
     *
     * @param  string|null  $pattern  缓存键模式，null为清除所有
     * @return bool
     */
    public function clearCache(?string $pattern = null): bool
    {
        try {
            if ($pattern) {
                // 这里需要根据你使用的缓存驱动实现模式匹配清除
                // Redis示例：Cache::getRedis()->del(Cache::getRedis()->keys("deeplx_*"));
                Log::info('清除指定模式的翻译缓存', ['pattern' => $pattern]);
            } else {
                Cache::flush();
                Log::info('清除所有翻译缓存');
            }
            return true;
        } catch (Exception $e) {
            Log::error('清除缓存失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 验证输入参数
     */
    private function validateInput(string $text, string $targetLang, ?string $sourceLang): void
    {
        if (empty(trim($text))) {
            throw new Exception('翻译文本不能为空');
        }

        if (mb_strlen($text) > 5000) {
            throw new Exception('翻译文本长度不能超过5000个字符');
        }

        $supportedLangs = array_keys($this->getSupportedLanguages());

        if (! in_array(strtoupper($targetLang), $supportedLangs)) {
            throw new Exception("不支持的目标语言: {$targetLang}");
        }

        if ($sourceLang && ! in_array(strtoupper($sourceLang), $supportedLangs)) {
            throw new Exception("不支持的源语言: {$sourceLang}");
        }
    }

    /**
     * 验证API响应格式
     */
    private function isValidResponse($response): bool
    {
        return is_array($response) &&
            (isset($response['data']) || isset($response['text']) || isset($response['translations']));
    }

    /**
     * 格式化响应数据
     */
    private function formatResponse(
        array $response,
        string $originalText,
        string $targetLang,
        ?string $sourceLang
    ): array {
        // 根据DeepLX的实际响应格式调整
        $translatedText = $response['data'] ?? $response['text'] ?? $response['translations'][0]['text'] ?? '';
        $detectedLang   = $response['source_lang'] ?? $sourceLang ?? 'auto';

        return [
            'original_text'   => $originalText,
            'translated_text' => $translatedText,
            'source_language' => $detectedLang,
            'target_language' => $targetLang,
            'timestamp'       => now()->toISOString(),
            'service'         => 'DeepLX',
            'cached'          => false
        ];
    }

    /**
     * 生成缓存键
     */
    private function generateCacheKey(string $text, string $targetLang, ?string $sourceLang): string
    {
        $key = 'deeplx_'.md5($text.$targetLang.($sourceLang ?? 'auto'));
        return $key;
    }
}