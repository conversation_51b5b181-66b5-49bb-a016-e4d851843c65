<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DeepLXTranslationService
{
    private string $apiUrl;
    private int    $timeout;
    private bool   $cacheEnabled;
    private int    $cacheTime;

    public function __construct()
    {
        $this->apiUrl       = config('services.deeplx.url');
        $this->timeout      = config('services.deeplx.timeout', 30);
        $this->cacheEnabled = config('services.deeplx.cache_enabled', true);
        $this->cacheTime    = config('services.deeplx.cache_time', 3600); // 1小时
    }

    /**
     * 翻译文本
     *
     * @param  string  $text  要翻译的文本
     * @param  string  $targetLang  目标语言代码
     * @param  string|null  $sourceLang  源语言代码，null为自动检测
     * @return array
     * @throws Exception
     */
    public function translate(string $text, string $targetLang, ?string $sourceLang = null): array
    {
        // 验证输入
        $this->validateInput($text, $targetLang, $sourceLang);

        // 生成缓存键
        $cacheKey = $this->generateCacheKey($text, $targetLang, $sourceLang);

        // 尝试从缓存获取
        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            Log::info('从缓存获取翻译结果', ['cache_key' => $cacheKey]);
            return Cache::get($cacheKey);
        }

        try {
            // 准备请求数据 - 使用DeepLX标准格式
            $requestData = [
                'text'        => $text,
                'target_lang' => strtoupper($targetLang),
            ];

            if ($sourceLang) {
                $requestData['source_lang'] = strtoupper($sourceLang);
            }

            // 添加可能的额外参数来确保返回纯译文
            $requestData['alternatives'] = 1;      // 只要一个翻译结果
            $requestData['format']       = 'text'; // 纯文本格式

            Log::info('发送翻译请求', $requestData);

            // 发送请求
            $response = Http::timeout($this->timeout)
                ->post($this->apiUrl, $requestData);

            if (! $response->successful()) {
                throw new Exception(
                    "DeepLX API请求失败: HTTP {$response->status()} - {$response->body()}"
                );
            }

            $result = $response->json();

            // 验证响应格式
            if (! $this->isValidResponse($result)) {
                throw new Exception('DeepLX API返回格式无效');
            }

            // 格式化响应
            $formattedResult = $this->formatResponse($result, $text, $targetLang, $sourceLang);

            // 缓存结果
            if ($this->cacheEnabled) {
                Cache::put($cacheKey, $formattedResult, $this->cacheTime);
                Log::info('翻译结果已缓存', ['cache_key' => $cacheKey]);
            }

            Log::info('翻译完成', ['original_text' => $text, 'translated_text' => $formattedResult['translated_text']]);

            return $formattedResult;
        } catch (Exception $e) {
            Log::error('翻译失败', [
                'error'       => $e->getMessage(),
                'text'        => $text,
                'target_lang' => $targetLang,
                'source_lang' => $sourceLang
            ]);
            throw $e;
        }
    }

    /**
     * 只返回翻译后的文本（不包含其他信息）
     *
     * @param  string  $text  要翻译的文本
     * @param  string  $targetLang  目标语言代码
     * @param  string|null  $sourceLang  源语言代码，null为自动检测
     * @return string 翻译后的文本
     * @throws Exception
     */
    public function translateText(string $text, string $targetLang, ?string $sourceLang = null): string
    {
        $result = $this->translate($text, $targetLang, $sourceLang);
        return $result['translated_text'];
    }

    /**
     * 快速翻译 - 直接返回纯译文，优化了DeepLX响应处理
     *
     * @param  string  $text  要翻译的文本
     * @param  string  $targetLang  目标语言代码
     * @param  string|null  $sourceLang  源语言代码，null为自动检测
     * @return string 纯译文
     * @throws Exception
     */
    public function quickTranslate(string $text, string $targetLang, ?string $sourceLang = null): string
    {
        // 验证输入
        $this->validateInput($text, $targetLang, $sourceLang);

        try {
            // 准备请求数据 - 优化为获取纯译文
            $requestData = [
                'text'         => $text,
                'target_lang'  => strtoupper($targetLang),
                'alternatives' => 1, // 只要一个翻译结果
                'format'       => 'text', // 纯文本格式
            ];

            if ($sourceLang) {
                $requestData['source_lang'] = strtoupper($sourceLang);
            }

            // 发送请求
            $response = Http::timeout($this->timeout)
                ->post($this->apiUrl, $requestData);

            if (! $response->successful()) {
                throw new Exception(
                    "DeepLX API请求失败: HTTP {$response->status()} - {$response->body()}"
                );
            }

            $result = $response->json();

            // 使用专门的方法提取纯译文
            return $this->extractPureTranslation($result, $text, $targetLang);
        } catch (Exception $e) {
            Log::error('快速翻译失败', [
                'error'       => $e->getMessage(),
                'text'        => $text,
                'target_lang' => $targetLang,
                'source_lang' => $sourceLang
            ]);
            throw $e;
        }
    }

    /**
     * 批量翻译并只返回翻译文本
     *
     * @param  array  $texts  文本数组
     * @param  string  $targetLang  目标语言
     * @param  string|null  $sourceLang  源语言
     * @return array 翻译后的文本数组
     */
    public function translateTexts(array $texts, string $targetLang, ?string $sourceLang = null): array
    {
        $results = [];

        foreach ($texts as $index => $text) {
            try {
                $results[$index] = $this->translateText($text, $targetLang, $sourceLang);
            } catch (Exception $e) {
                $results[$index] = $text; // 翻译失败时返回原文
                Log::warning('翻译失败，返回原文', [
                    'index' => $index,
                    'text'  => $text,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * @param  array  $texts  文本数组
     * @param  string  $targetLang  目标语言
     * @param  string|null  $sourceLang  源语言
     * @return array
     */
    public function translateBatch(array $texts, string $targetLang, ?string $sourceLang = null): array
    {
        $results = [];
        $errors  = [];

        foreach ($texts as $index => $text) {
            try {
                $results[$index] = $this->translate($text, $targetLang, $sourceLang);
            } catch (Exception $e) {
                $errors[$index] = [
                    'original_text' => $text,
                    'error'         => $e->getMessage(),
                    'index'         => $index
                ];
            }
        }

        return [
            'successful_translations' => $results,
            'failed_translations'     => $errors,
            'total_count'             => count($texts),
            'success_count'           => count($results),
            'error_count'             => count($errors)
        ];
    }

    /**
     * 检测语言
     *
     * @param  string  $text
     * @return array
     * @throws \Exception
     */
    public function detectLanguage(string $text): array
    {
        try {
            // 使用翻译接口进行语言检测（通过翻译成英文）
            $result = $this->translate($text, 'EN');
            return [
                'detected_language' => $result['source_language'] ?? 'unknown',
                'confidence'        => $result['confidence'] ?? null
            ];
        } catch (Exception $e) {
            Log::error('语言检测失败', ['error' => $e->getMessage(), 'text' => $text]);
            throw new Exception('语言检测失败: '.$e->getMessage());
        }
    }

    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public function getSupportedLanguages(): array
    {
        return [
            'EN' => 'English',
            'ZH' => 'Chinese',
            'JA' => 'Japanese',
            'FR' => 'French',
            'DE' => 'German',
            'ES' => 'Spanish',
            'IT' => 'Italian',
            'PT' => 'Portuguese',
            'RU' => 'Russian',
            'KO' => 'Korean',
            'AR' => 'Arabic',
            'NL' => 'Dutch',
            'PL' => 'Polish',
            'TR' => 'Turkish'
        ];
    }

    /**
     * 清除翻译缓存
     *
     * @param  string|null  $pattern  缓存键模式，null为清除所有
     * @return bool
     */
    public function clearCache(?string $pattern = null): bool
    {
        try {
            if ($pattern) {
                // 这里需要根据你使用的缓存驱动实现模式匹配清除
                // Redis示例：Cache::getRedis()->del(Cache::getRedis()->keys("deeplx_*"));
                Log::info('清除指定模式的翻译缓存', ['pattern' => $pattern]);
            } else {
                Cache::flush();
                Log::info('清除所有翻译缓存');
            }
            return true;
        } catch (Exception $e) {
            Log::error('清除缓存失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 验证输入参数
     */
    private function validateInput(string $text, string $targetLang, ?string $sourceLang): void
    {
        if (empty(trim($text))) {
            throw new Exception('翻译文本不能为空');
        }

        if (mb_strlen($text) > 5000) {
            throw new Exception('翻译文本长度不能超过5000个字符');
        }

        $supportedLangs = array_keys($this->getSupportedLanguages());

        if (! in_array(strtoupper($targetLang), $supportedLangs)) {
            throw new Exception("不支持的目标语言: {$targetLang}");
        }

        if ($sourceLang && ! in_array(strtoupper($sourceLang), $supportedLangs)) {
            throw new Exception("不支持的源语言: {$sourceLang}");
        }
    }

    /**
     * 验证API响应格式
     */
    private function isValidResponse($response): bool
    {
        return is_array($response) &&
            (isset($response['data']) || isset($response['text']) || isset($response['translations']));
    }

    /**
     * 格式化响应数据
     */
    private function formatResponse(
        array $response,
        string $originalText,
        string $targetLang,
        ?string $sourceLang
    ): array {
        // DeepLX的标准响应格式：{ "code": 200, "id": "xxx", "data": "译文", "alternatives": [...] }
        $translatedText = '';

        // 优先处理DeepLX的标准格式
        if (isset($response['data']) && is_string($response['data'])) {
            // DeepLX的data字段通常就是纯译文
            $translatedText = trim($response['data']);

            // 记录成功提取
            Log::debug('DeepLX标准格式提取成功', [
                'data_field'    => $translatedText,
                'original_text' => $originalText
            ]);
        } elseif (isset($response['alternatives']) && is_array($response['alternatives']) && ! empty($response['alternatives'])) {
            // 备选方案：使用alternatives数组的第一个
            $translatedText = trim($response['alternatives'][0] ?? '');
            Log::debug('使用DeepLX alternatives字段', ['alternatives' => $response['alternatives']]);
        } elseif (isset($response['text'])) {
            // 兼容其他可能的格式
            $translatedText = trim($response['text']);
        } elseif (isset($response['translations']) && is_array($response['translations'])) {
            // 兼容标准DeepL API格式
            $translatedText = trim($response['translations'][0]['text'] ?? '');
        } elseif (is_string($response)) {
            // 直接字符串响应
            $translatedText = trim($response);
        }

        // 记录原始响应用于调试
        Log::debug('DeepLX原始响应', [
            'response'       => $response,
            'extracted_text' => $translatedText,
            'original_text'  => $originalText
        ]);

        // 验证提取的文本
        if (empty($translatedText)) {
            Log::warning('DeepLX响应中未找到翻译文本', ['response' => $response]);
            throw new Exception('DeepLX API返回的翻译文本为空');
        }

        // 如果提取的文本看起来仍然包含原文和译文的混合，进行清理
        $cleanTranslatedText = $this->smartExtractTranslation($translatedText, $originalText, $targetLang);

        // 获取检测到的源语言
        $detectedLang = $response['source_lang'] ?? $response['detected_source_language'] ?? $sourceLang ?? 'auto';

        Log::info('翻译处理完成', [
            'original'          => $originalText,
            'raw_translation'   => $translatedText,
            'clean_translation' => $cleanTranslatedText
        ]);

        return [
            'original_text'   => $originalText,
            'translated_text' => $cleanTranslatedText,
            'source_language' => $detectedLang,
            'target_language' => $targetLang,
            'timestamp'       => now()->toISOString(),
            'service'         => 'DeepLX',
            'cached'          => false,
            'raw_response'    => $translatedText // 保留原始响应用于调试
        ];
    }

    /**
     * 提取纯译文 - 专门处理DeepLX响应
     */
    private function extractPureTranslation(array $response, string $originalText, string $targetLang): string
    {
        // 方案1: 直接提取data字段（最常见的DeepLX格式）
        if (isset($response['data']) && is_string($response['data'])) {
            $translatedText = trim($response['data']);

            // 检查是否是纯译文（不包含原文）
            if (! str_contains($translatedText, $originalText)) {
                Log::debug('DeepLX返回纯译文', ['data' => $translatedText]);
                return $translatedText;
            }

            // 如果包含原文，进行智能提取
            Log::debug('DeepLX返回混合文本，进行提取', ['mixed_text' => $translatedText]);
            return $this->smartExtractTranslation($translatedText, $originalText, $targetLang);
        }

        // 方案2: 使用alternatives数组
        if (isset($response['alternatives']) && is_array($response['alternatives']) && ! empty($response['alternatives'])) {
            $translatedText = trim($response['alternatives'][0]);
            Log::debug('使用DeepLX alternatives字段', ['alternatives' => $response['alternatives']]);

            if (! str_contains($translatedText, $originalText)) {
                return $translatedText;
            }

            return $this->smartExtractTranslation($translatedText, $originalText, $targetLang);
        }

        // 方案3: 兼容其他可能的格式
        if (isset($response['text'])) {
            $translatedText = trim($response['text']);
            return ! str_contains($translatedText, $originalText)
                ? $translatedText
                : $this->smartExtractTranslation($translatedText, $originalText, $targetLang);
        }

        // 方案4: 标准DeepL API格式
        if (isset($response['translations']) && is_array($response['translations'])) {
            $translatedText = trim($response['translations'][0]['text'] ?? '');
            return ! str_contains($translatedText, $originalText)
                ? $translatedText
                : $this->smartExtractTranslation($translatedText, $originalText, $targetLang);
        }

        // 如果都没有找到，记录错误并抛出异常
        Log::error('DeepLX响应格式无效', [
            'response'       => $response,
            'available_keys' => array_keys($response)
        ]);

        throw new Exception('DeepLX API返回格式无效：未找到翻译文本字段');
    }

    /**
     * 智能提取翻译文本 - 新的优化方法
     */
    private function smartExtractTranslation(string $text, string $originalText, string $targetLang): string
    {
        $text         = trim($text);
        $originalText = trim($originalText);

        // 如果文本就是纯译文（不包含原文），直接返回
        if (! str_contains($text, $originalText)) {
            return $text;
        }

        // 如果文本与原文相同，说明没有翻译，返回原文
        if ($text === $originalText) {
            return $text;
        }

        // 尝试各种分隔符模式提取
        $separators = [
            ' -> ', ' → ', ' - ', ' | ', '：', ': ',
            '\n', '\\n', '\r\n', '\\r\\n',
            ' — ', ' – ', ' => ', ' = '
        ];

        foreach ($separators as $separator) {
            if (str_contains($text, $separator)) {
                $parts = explode($separator, $text, 2);
                if (count($parts) >= 2) {
                    $firstPart  = trim($parts[0]);
                    $secondPart = trim($parts[1]);

                    // 如果第一部分是原文，返回第二部分
                    if ($firstPart === $originalText) {
                        return $secondPart;
                    }

                    // 如果第二部分是原文，返回第一部分
                    if ($secondPart === $originalText) {
                        return $firstPart;
                    }

                    // 根据目标语言智能选择
                    if ($targetLang === 'ZH' || $targetLang === 'zh') {
                        // 中文翻译：选择包含更多中文字符的部分
                        if ($this->isChineseText($secondPart) && ! $this->isChineseText($firstPart)) {
                            return $secondPart;
                        }
                        if ($this->isChineseText($firstPart) && ! $this->isChineseText($secondPart)) {
                            return $firstPart;
                        }
                    }
                }
            }
        }

        // 如果是中文翻译，尝试提取纯中文内容
        if ($targetLang === 'ZH' || $targetLang === 'zh') {
            $chineseOnly = $this->extractChineseOnly($text);
            if (! empty($chineseOnly) && $chineseOnly !== $originalText) {
                return $chineseOnly;
            }
        }

        // 最后的清理：移除原文部分
        $cleaned = str_replace($originalText, '', $text);
        $cleaned = preg_replace('/^[\s\-=>\|：:]+/', '', $cleaned);
        $cleaned = preg_replace('/[\s\-=>\|：:]+$/', '', $cleaned);
        $cleaned = trim($cleaned);

        return ! empty($cleaned) ? $cleaned : $text;
    }

    /**
     * 专门提取中文内容
     */
    private function extractChineseOnly(string $text): string
    {
        // 使用正则表达式提取所有中文字符、标点符号
        $pattern = '/[\x{4e00}-\x{9fff}\x{ff0c}\x{3002}\x{ff1f}\x{ff01}\x{ff1a}\x{ff1b}\x{201c}\x{201d}\x{2018}\x{2019}\x{ff08}\x{ff09}\x{3001}\s]+/u';
        preg_match_all($pattern, $text, $matches);

        if (! empty($matches[0])) {
            // 合并所有中文片段并清理
            $chineseText = implode('', $matches[0]);
            $chineseText = preg_replace('/\s+/', ' ', $chineseText); // 保留必要空格
            $chineseText = trim($chineseText);

            return $chineseText;
        }

        return '';
    }

    /**
     * 检查文本是否主要包含中文
     */
    private function isChineseText(string $text): bool
    {
        if (empty($text)) {
            return false;
        }

        // 统计中文字符数量
        preg_match_all('/[\x{4e00}-\x{9fff}]/u', $text, $matches);
        $chineseCount = count($matches[0]);

        // 如果中文字符占比超过30%，认为是中文文本
        $totalLength = mb_strlen($text, 'UTF-8');
        return $totalLength > 0 && ($chineseCount / $totalLength) > 0.3;
    }

    /**
     * 测试DeepLX连接和响应格式
     */
    public function testDeepLXConnection(): array
    {
        try {
            $testText = 'Hello, world!';
            $response = Http::timeout($this->timeout)
                ->post($this->apiUrl, [
                    'text'         => $testText,
                    'target_lang'  => 'ZH',
                    'alternatives' => 1,
                    'format'       => 'text'
                ]);

            $result = [
                'success'       => $response->successful(),
                'status_code'   => $response->status(),
                'raw_response'  => $response->body(),
                'json_response' => $response->json(),
                'api_url'       => $this->apiUrl,
                'test_text'     => $testText
            ];

            if ($response->successful()) {
                $jsonData                         = $response->json();
                $result['extracted_data']         = $jsonData['data'] ?? null;
                $result['extracted_alternatives'] = $jsonData['alternatives'] ?? null;
                $result['response_structure']     = array_keys($jsonData);
            }

            return $result;
        } catch (Exception $e) {
            return [
                'success' => false,
                'error'   => $e->getMessage(),
                'api_url' => $this->apiUrl
            ];
        }
    }

    /**
     * 调试翻译结果 - 用于查看处理过程
     */
    public function debugTranslation(string $text, string $targetLang, ?string $sourceLang = null): array
    {
        try {
            // 准备请求数据
            $requestData = [
                'text'        => $text,
                'target_lang' => strtoupper($targetLang),
            ];

            if ($sourceLang) {
                $requestData['source_lang'] = strtoupper($sourceLang);
            }

            // 发送请求
            $response = Http::timeout($this->timeout)
                ->post($this->apiUrl, $requestData);

            $rawResponse = $response->json();

            // 提取原始翻译文本
            $rawTranslatedText = '';
            if (isset($rawResponse['data'])) {
                $rawTranslatedText = $rawResponse['data'];
            } elseif (isset($rawResponse['text'])) {
                $rawTranslatedText = $rawResponse['text'];
            } elseif (isset($rawResponse['alternatives']) && is_array($rawResponse['alternatives'])) {
                $rawTranslatedText = $rawResponse['alternatives'][0] ?? '';
            }

            // 应用清理逻辑
            $cleanedText = $this->smartExtractTranslation($rawTranslatedText, $text, $targetLang);

            // 如果是中文翻译，额外提取中文
            $chineseOnly = '';
            if (strtoupper($targetLang) === 'ZH') {
                $chineseOnly = $this->extractChineseOnly($rawTranslatedText);
            }

            return [
                'original_text'       => $text,
                'raw_api_response'    => $rawResponse,
                'raw_translated_text' => $rawTranslatedText,
                'cleaned_text'        => $cleanedText,
                'chinese_only'        => $chineseOnly,
                'final_result'        => ! empty($chineseOnly) ? $chineseOnly : $cleanedText,
                'processing_steps'    => [
                    'step1_raw_extraction'     => $rawTranslatedText,
                    'step2_text_cleaning'      => $cleanedText,
                    'step3_chinese_extraction' => $chineseOnly,
                    'step4_final_choice'       => ! empty($chineseOnly) ? $chineseOnly : $cleanedText
                ]
            ];
        } catch (Exception $e) {
            return [
                'error'         => $e->getMessage(),
                'original_text' => $text
            ];
        }
    }

    private function generateCacheKey(string $text, string $targetLang, ?string $sourceLang): string
    {
        $key = 'deeplx_'.md5($text.$targetLang.($sourceLang ?? 'auto'));
        return $key;
    }
}