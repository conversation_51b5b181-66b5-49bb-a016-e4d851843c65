<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DeepLXTranslationService
{
    private string $apiUrl;
    private int    $timeout;
    private bool   $cacheEnabled;
    private int    $cacheTime;

    public function __construct()
    {
        $this->apiUrl       = config('services.deeplx.url', 'http://localhost:1188/translate');
        $this->timeout      = config('services.deeplx.timeout', 30);
        $this->cacheEnabled = config('services.deeplx.cache_enabled', true);
        $this->cacheTime    = config('services.deeplx.cache_time', 3600); // 1小时
    }

    /**
     * 纯内容翻译 - 只翻译英文内容，保持所有格式和标记
     * 直接调用DeepLX API，不做任何后处理，适用于prompt等需要保持原始格式的场景
     * 
     * @param  string  $text  要翻译的文本
     * @param  string  $targetLang  目标语言代码
     * @param  string|null  $sourceLang  源语言代码
     * @return string 翻译后的文本，保持原有格式
     * @throws Exception
     */
    public function translateContentOnly(string $text, string $targetLang, ?string $sourceLang = null): string
    {
        // 验证输入
        $this->validateInput($text, $targetLang, $sourceLang);

        try {
            // 准备请求数据 - 最简单的格式，不添加任何额外参数
            $requestData = [
                'text' => $text,
                'target_lang' => strtoupper($targetLang),
            ];

            if ($sourceLang) {
                $requestData['source_lang'] = strtoupper($sourceLang);
            }

            Log::info('纯内容翻译请求', $requestData);

            // 发送请求
            $response = Http::timeout($this->timeout)
                ->post($this->apiUrl, $requestData);

            if (!$response->successful()) {
                throw new Exception(
                    "DeepLX API请求失败: HTTP {$response->status()} - {$response->body()}"
                );
            }

            $result = $response->json();
            
            Log::info('DeepLX原始响应', ['response' => $result]);

            // 直接提取data字段，不做任何后处理
            if (isset($result['data']) && is_string($result['data'])) {
                $translatedText = trim($result['data']);
                Log::info('提取到翻译文本', ['translated' => $translatedText]);
                return $translatedText;
            }

            // 备选方案
            if (isset($result['alternatives']) && is_array($result['alternatives']) && !empty($result['alternatives'])) {
                $translatedText = trim($result['alternatives'][0]);
                Log::info('从alternatives提取翻译文本', ['translated' => $translatedText]);
                return $translatedText;
            }

            throw new Exception('DeepLX API返回格式无效：未找到翻译文本');

        } catch (Exception $e) {
            Log::error('纯内容翻译失败', [
                'error' => $e->getMessage(),
                'text' => $text,
                'target_lang' => $targetLang,
                'source_lang' => $sourceLang
            ]);
            throw $e;
        }
    }

    /**
     * 快速翻译 - 直接返回纯译文
     */
    public function quickTranslate(string $text, string $targetLang, ?string $sourceLang = null): string
    {
        return $this->translateContentOnly($text, $targetLang, $sourceLang);
    }

    /**
     * 测试DeepLX连接和响应格式
     */
    public function testDeepLXConnection(): array
    {
        try {
            $testText = 'Hello, world!';
            $response = Http::timeout($this->timeout)
                ->post($this->apiUrl, [
                    'text' => $testText,
                    'target_lang' => 'ZH'
                ]);

            $result = [
                'success' => $response->successful(),
                'status_code' => $response->status(),
                'raw_response' => $response->body(),
                'json_response' => $response->json(),
                'api_url' => $this->apiUrl,
                'test_text' => $testText
            ];

            if ($response->successful()) {
                $jsonData = $response->json();
                $result['extracted_data'] = $jsonData['data'] ?? null;
                $result['extracted_alternatives'] = $jsonData['alternatives'] ?? null;
                $result['response_structure'] = array_keys($jsonData);
            }

            return $result;

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'api_url' => $this->apiUrl
            ];
        }
    }

    /**
     * 验证输入参数
     */
    private function validateInput(string $text, string $targetLang, ?string $sourceLang): void
    {
        if (empty(trim($text))) {
            throw new Exception('翻译文本不能为空');
        }

        if (empty($targetLang)) {
            throw new Exception('目标语言不能为空');
        }

        if (mb_strlen($text) > 10000) {
            throw new Exception('文本长度不能超过10000字符');
        }
    }
}
