<?php

namespace App\Packages\Plugin;

class PluginResponse
{

    public function __construct(
        protected bool $success,
        protected string $message,
        protected array $data,
        protected array $params = []
    ) {
    }

    /**
     * @param  string  $message
     * @param  array  $params
     * @return \App\Packages\Plugin\PluginResponse
     */
    public static function error(string $message, array $params): PluginResponse
    {
        return new static(false, $message, [], $params);
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }
}