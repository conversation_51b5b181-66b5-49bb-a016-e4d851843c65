<?php

namespace App\MCP\Tools;

use App\Jobs\Assistant\SendEmailToolJob;
use App\MCP\ToolsBase;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;
use Validator;

class SendToMailTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'send_to_mail';
    }

    public function description(): string
    {
        return '发送信息到指定邮箱';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'email'    => [
                    'type'        => 'string',
                    'description' => '目标EMAIL地址',
                ],
                'title'    => [
                    'type'        => 'string',
                    'description' => '邮件标题',
                ],
                'content'  => [
                    'type'        => 'string',
                    'description' => '邮件内容可以使用Markdown格式',
                ],
                'wateauth' => [
                    'type'        => 'string',
                    'description' => '鉴权信息',
                ],
            ],
            'required'   => ['email', 'title', 'content', 'wateauth'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'SendToMailTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $user    = $this->getUser($arguments);
            $toEmail = str_replace("\${email}", $user->email, $arguments['email']);
            $valida  = Validator::make(['email' => $toEmail],
                [
                    'email' => 'required|email'
                ], [
                    'email.required' => '请输入邮箱地址',
                    'email.email'    => '请输入正确的邮箱地址'
                ]);
            if ($valida->fails()) {
                return $this->error($valida->errors()->first());
            }
            $title   = $arguments['title'];
            $content = $arguments['content'];
            SendEmailToolJob::dispatch($user, $toEmail, $title, $content, 'ep-20250611165125-7dhbg', 16300);

            return $this->success(['job_name' => 'SendEmailToolJob'],
                '邮件会经过模型优化HTML页面再发送（时间较长），已加入邮件队列，请稍后查看邮件');
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
