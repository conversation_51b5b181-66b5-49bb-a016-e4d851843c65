<?php

/**
 * 模拟 DeepLX 响应测试
 * 测试我们的文本提取逻辑
 */

// 模拟不同的 DeepLX 响应格式
$mockResponses = [
    // 理想情况：纯译文
    'pure_translation' => [
        'code' => 200,
        'id' => 'test-1',
        'data' => '你好，世界！',
        'alternatives' => ['你好，世界！']
    ],
    
    // 问题情况：混合文本
    'mixed_text_arrow' => [
        'code' => 200,
        'id' => 'test-2',
        'data' => 'Hello, world! -> 你好，世界！',
        'alternatives' => ['Hello, world! -> 你好，世界！']
    ],
    
    // 问题情况：混合文本（冒号分隔）
    'mixed_text_colon' => [
        'code' => 200,
        'id' => 'test-3',
        'data' => 'Hello, world!：你好，世界！',
        'alternatives' => ['Hello, world!：你好，世界！']
    ],
    
    // 问题情况：混合文本（换行分隔）
    'mixed_text_newline' => [
        'code' => 200,
        'id' => 'test-4',
        'data' => "Hello, world!\n你好，世界！",
        'alternatives' => ["Hello, world!\n你好，世界！"]
    ],
    
    // 备选格式：只有 alternatives
    'alternatives_only' => [
        'code' => 200,
        'id' => 'test-5',
        'alternatives' => ['你好，世界！']
    ],
    
    // 标准 DeepL 格式
    'deepl_format' => [
        'translations' => [
            ['text' => '你好，世界！']
        ]
    ]
];

function extractPureTranslation($response, $originalText, $targetLang) {
    // 方案1: 直接提取data字段
    if (isset($response['data']) && is_string($response['data'])) {
        $translatedText = trim($response['data']);
        
        // 检查是否是纯译文（不包含原文）
        if (strpos($translatedText, $originalText) === false) {
            echo "  ✅ 发现纯译文: {$translatedText}\n";
            return $translatedText;
        }
        
        // 如果包含原文，进行智能提取
        echo "  ⚠️  发现混合文本: {$translatedText}\n";
        return smartExtractTranslation($translatedText, $originalText, $targetLang);
    }

    // 方案2: 使用alternatives数组
    if (isset($response['alternatives']) && is_array($response['alternatives']) && !empty($response['alternatives'])) {
        $translatedText = trim($response['alternatives'][0]);
        echo "  📝 使用 alternatives 字段: {$translatedText}\n";
        
        if (strpos($translatedText, $originalText) === false) {
            return $translatedText;
        }
        
        return smartExtractTranslation($translatedText, $originalText, $targetLang);
    }

    // 方案3: 兼容其他格式
    if (isset($response['text'])) {
        $translatedText = trim($response['text']);
        return strpos($translatedText, $originalText) === false 
            ? $translatedText 
            : smartExtractTranslation($translatedText, $originalText, $targetLang);
    }

    // 方案4: 标准DeepL API格式
    if (isset($response['translations']) && is_array($response['translations'])) {
        $translatedText = trim($response['translations'][0]['text'] ?? '');
        return strpos($translatedText, $originalText) === false 
            ? $translatedText 
            : smartExtractTranslation($translatedText, $originalText, $targetLang);
    }

    throw new Exception('未找到翻译文本字段');
}

function smartExtractTranslation($text, $originalText, $targetLang) {
    $text = trim($text);
    $originalText = trim($originalText);

    echo "  🔍 开始智能提取...\n";
    echo "     原文: {$originalText}\n";
    echo "     混合文本: {$text}\n";

    // 如果文本与原文相同，说明没有翻译
    if ($text === $originalText) {
        echo "  ❌ 文本与原文相同，未翻译\n";
        return $text;
    }

    // 尝试各种分隔符模式提取
    $separators = [
        ' -> ', ' → ', ' - ', ' | ', '：', ': ', 
        '\n', "\\n", '\r\n', "\\r\\n", 
        ' — ', ' – ', ' => ', ' = '
    ];

    foreach ($separators as $separator) {
        if (strpos($text, $separator) !== false) {
            $parts = explode($separator, $text, 2);
            if (count($parts) >= 2) {
                $firstPart = trim($parts[0]);
                $secondPart = trim($parts[1]);

                echo "     尝试分隔符 '{$separator}': '{$firstPart}' | '{$secondPart}'\n";

                // 如果第一部分是原文，返回第二部分
                if ($firstPart === $originalText) {
                    echo "  ✅ 找到译文（第二部分）: {$secondPart}\n";
                    return $secondPart;
                }

                // 如果第二部分是原文，返回第一部分
                if ($secondPart === $originalText) {
                    echo "  ✅ 找到译文（第一部分）: {$firstPart}\n";
                    return $firstPart;
                }

                // 根据目标语言智能选择
                if ($targetLang === 'ZH' || $targetLang === 'zh') {
                    // 中文翻译：选择包含更多中文字符的部分
                    if (isChineseText($secondPart) && !isChineseText($firstPart)) {
                        echo "  ✅ 根据中文特征选择: {$secondPart}\n";
                        return $secondPart;
                    }
                    if (isChineseText($firstPart) && !isChineseText($secondPart)) {
                        echo "  ✅ 根据中文特征选择: {$firstPart}\n";
                        return $firstPart;
                    }
                }
            }
        }
    }

    // 如果是中文翻译，尝试提取纯中文内容
    if ($targetLang === 'ZH' || $targetLang === 'zh') {
        $chineseOnly = extractChineseOnly($text);
        if (!empty($chineseOnly) && $chineseOnly !== $originalText) {
            echo "  ✅ 提取中文内容: {$chineseOnly}\n";
            return $chineseOnly;
        }
    }

    // 最后的清理：移除原文部分
    $cleaned = str_replace($originalText, '', $text);
    $cleaned = preg_replace('/^[\s\-=>\|：:]+/', '', $cleaned);
    $cleaned = preg_replace('/[\s\-=>\|：:]+$/', '', $cleaned);
    $cleaned = trim($cleaned);

    if (!empty($cleaned)) {
        echo "  ✅ 清理后的文本: {$cleaned}\n";
        return $cleaned;
    }

    echo "  ❌ 无法提取，返回原始文本\n";
    return $text;
}

function isChineseText($text) {
    if (empty($text)) {
        return false;
    }

    // 统计中文字符数量
    preg_match_all('/[\x{4e00}-\x{9fff}]/u', $text, $matches);
    $chineseCount = count($matches[0]);

    // 如果中文字符占比超过30%，认为是中文文本
    $totalLength = mb_strlen($text, 'UTF-8');
    return $totalLength > 0 && ($chineseCount / $totalLength) > 0.3;
}

function extractChineseOnly($text) {
    // 使用正则表达式提取所有中文字符、标点符号
    $pattern = '/[\x{4e00}-\x{9fff}\x{ff0c}\x{3002}\x{ff1f}\x{ff01}\x{ff1a}\x{ff1b}\x{201c}\x{201d}\x{2018}\x{2019}\x{ff08}\x{ff09}\x{3001}\s]+/u';
    preg_match_all($pattern, $text, $matches);

    if (!empty($matches[0])) {
        // 合并所有中文片段并清理
        $chineseText = implode('', $matches[0]);
        $chineseText = preg_replace('/\s+/', ' ', $chineseText); // 保留必要空格
        $chineseText = trim($chineseText);

        return $chineseText;
    }

    return '';
}

echo "=== 模拟 DeepLX 响应测试 ===\n\n";

$originalText = 'Hello, world!';
$targetLang = 'ZH';

foreach ($mockResponses as $testName => $response) {
    echo "测试: {$testName}\n";
    echo "模拟响应: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    
    try {
        $result = extractPureTranslation($response, $originalText, $targetLang);
        echo "🎯 最终结果: {$result}\n";
    } catch (Exception $e) {
        echo "❌ 错误: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

echo "=== 测试完成 ===\n";
