<?php

require_once 'vendor/autoload.php';

use App\Services\DeepLXTranslationService;

// 创建服务实例
$service = new DeepLXTranslationService();

echo "=== DeepLX Translation Service Test ===\n\n";

// 首先测试连接
echo "1. Testing DeepLX Connection...\n";
$connectionTest = $service->testDeepLXConnection();
echo "Connection Result:\n";
print_r($connectionTest);
echo "\n" . str_repeat("-", 50) . "\n\n";

// 测试文本
$testTexts = [
    'Hello, world!',
    'How are you today?',
    'This is a test message for translation.',
    'Good morning, have a nice day!'
];

foreach ($testTexts as $index => $text) {
    echo "Test " . ($index + 1) . ":\n";
    echo "Original: {$text}\n";
    
    try {
        // 测试快速翻译方法
        $quickResult = $service->quickTranslate($text, 'ZH');
        echo "Quick Translate: {$quickResult}\n";
        
        // 测试完整翻译方法
        $fullResult = $service->translate($text, 'ZH');
        echo "Full Translate: {$fullResult['translated_text']}\n";
        echo "Source Language: {$fullResult['source_language']}\n";
        
        // 测试调试方法
        $debugResult = $service->debugTranslation($text, 'ZH');
        echo "Debug Info:\n";
        echo "  Raw API Response: " . json_encode($debugResult['raw_api_response']) . "\n";
        echo "  Final Result: {$debugResult['final_result']}\n";
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "=== Test Completed ===\n";
